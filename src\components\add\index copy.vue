<template>
  <div
    class="chinese-num-box"
    :style="widthStyle"
  >
    <!-- 条形码扫描器组件 -->
    <div v-if="showBarcodeScanner" class="barcode-scanner-container" >
      <!-- 文本框和扫描按钮的容器 -->
      <div class="input-scan-container">
        <!-- 文本框 -->
        <div class="input-wrapper">
          <input
            v-model="number"
            type="text"
            class="scan-input"
            placeholder="请输入或扫描条形码"
            @input="onInputChange"
          />
          <button
            v-if="number"
            class="clear-input-button"
            @click="clearInput"
            title="清除内容"
          >
            ×
          </button>
        </div>
        <!-- 扫描按钮 -->
        <div class="scan-icon" @click="startScan" title="扫描条形码">
          <svg viewBox="0 0 24 24" width="28" height="28">
            <path fill="currentColor" d="M3,3h6v2H5v4H3V3z M3,21h6v-2H5v-4H3V21z M21,3v6h-2V5h-4V3H21z M21,21v-6h-2v4h-4v2H21z"/>
          </svg>
        </div>
      </div>

      <!-- 使用相机扫码的模态框 -->
      <div v-if="showScanner" class="scanner-modal">
        <div class="scanner-container">
          <div class="scanner-header">
            <h3>扫描条形码</h3>
            <button class="close-button" @click="closeScanner">×</button>
          </div>
          <div class="scanner-content">
            <!-- 相机扫描模式 -->
            <div v-if="!cameraError" class="camera-container">
              <div id="interactive" class="viewport">
                <!-- 移除扫描区域指示器和扫描线，全屏扫描 -->
              </div>
              <div class="scan-tip">
                <p><strong>扫描提示：</strong></p>
                <ul>
                  <li>请将条形码<strong>水平放置</strong>，保持平整</li>
                  <li>确保光线充足，避免强光反射</li>
                  <li>保持手机稳定，距离条形码约10-15厘米</li>
                  <li>支持韵达、中通、申通、圆通、顺丰等快递条形码</li>
                <li v-if="availableCameras.length > 1">如果扫描效果不佳，可尝试<strong>切换摄像头</strong></li>
                <li v-if="isMobileDevice">当前使用{{ useFrontCamera ? '前置' : '后置' }}摄像头，后置摄像头通常效果更好</li>
              </ul>
            </div>
            <div class="dynamic-tip" id="dynamicTip"></div>
            <div class="scan-actions">
              <button class="action-button" @click="toggleFlash" v-if="hasFlash">
                <span v-if="!flashOn">打开闪光灯</span>
                <span v-else>关闭闪光灯</span>
              </button>
              <button class="action-button" @click="switchCamera" v-if="availableCameras.length > 1">
                <span v-if="isMobileDevice">
                  {{ useFrontCamera ? '切换到后置摄像头' : '切换到前置摄像头' }}
                </span>
                <span v-else>切换摄像头</span>
              </button>
            </div>
          </div>
          <!-- 错误提示 -->
          <div v-if="cameraError" class="camera-error-container">
            <div class="camera-error-message" v-html="cameraErrorMessage"></div>
            <div class="error-actions">
              <button class="retry-button" @click="startScan">重试</button>
            </div>
          </div>
        </div>
      </div>
    </div>


    <el-tooltip v-if="tipsChecked" class="item" effect="dark" :content="tipsContent" placement="top">
      <template slot="content">
        <div v-html="tipsContent"></div>
      </template>
      <img :src="tipsImg" />
    </el-tooltip>
    <div></div>
  </div>
</div>
</template>

<script>
import { vueMixin } from "sdata-plugin-adapter";
import eventActionDefinitions from "./eventActionDefinitions.js";
import { OPERATION_PROPERTIES } from '../../utils';
import Quagga from 'quagga';

export default {
  name: "Main",
  props: {
    vueProps: Object
  },
  mixins: [vueMixin()],
  data() {
    return {
      customConfig: "",
      eventActionDefinitions,
      // 储存逻辑控制-动作,这里对应的是eventActionDefinitions，中注册的两个组件动作的key
      actions: {
        getNumber: this.getNumber,
        getChineseNumber: this.getChineseNumber,
        message: this.message,
        startScan: this.startScan
      },
      number: null,
      componentConfig: {},
      dataValue: '',
      // 条形码扫描器相关数据
      showScanner: false,
      scanResult: null,
      scanResults: [], // 存储多个扫描结果
      cameraError: false,
      cameraErrorMessage: '',
      quaggaInitialized: false,
      scanning: false,
      lastResults: [], // 存储最近的扫描结果用于验证
      allDetectedCodes: [], // 存储所有检测到的条形码
      scanStartTime: null, // 扫描开始时间
      formatWarningShown: false, // 是否已经显示了格式警告
      hasFlash: false, // 是否支持闪光灯
      flashOn: false, // 闪光灯是否开启
      stream: null, // 存储视频流引用
      isMobileDevice: false, // 是否为移动设备
      useFrontCamera: false, // 是否使用前置摄像头
      availableCameras: [], // 可用的摄像头列表
      selectedCameraId: null // 选中的摄像头ID
    };
  },

  mounted() {
    console.log(123123, this.vueProps);
    // 自定义配置项内容，我们在designConfiguration中定义了是否使用控制按钮
    this.customConfig = this.vueProps?.customConfig || {};
    // componentConfig此属性为复用的基线支持的配置项，具体配置在config.json中props的componentAttributes属性
    this.componentConfig = this.vueProps?.component?.componentConfig || {};

    // 初始化数据值
    if (this.vueProps?.data) {
      this.dataValue = this.vueProps.data;
      try {
        this.number = JSON.parse(this.vueProps.data);
      } catch (e) {
        console.warn('解析数据失败:', e);
        this.number = null;
      }
    }
  },

  computed: {
    // 这个是基线配置项中宽度的
    widthStyle() {
      const { width, width_unit } = this.componentConfig || {};
      return {
        width: `${width ?? 100}${width_unit ?? "%"}`
      }
    },
    // 这个是基线配置项交互中禁用
    disabled() {
      const { operate_attribute } = this.componentConfig || {};
      return operate_attribute
      ? operate_attribute.split(",").includes(OPERATION_PROPERTIES.DISABLED)
      : false;
    },
    // 交互中的步长
    step() {
      return (this.componentConfig || {}).step || 1;
    },
    // 配置项，样式中占位提示
    tips() {
      return (this.componentConfig || {}).tips;
    },
    // 提示图片
    tipsImg() {
      return require('../img/tips-icon.svg')
    },
    // 配置项，样式中提示内容,是否打开，这个是个boolean制
    tipsChecked() {
      return (this.componentConfig || {}).tips_checked;
    },
    // 配置项，样式中提示内容
    tipsContent() {
      return (this.componentConfig || {}).tips_content;
    },
    // 配置项，交互，最小值
    numMinValue() {
      return (this.componentConfig || {}).num_min_value;
    },
    // 配置项，交互，最大值
    numMaxValue() {
      return (this.componentConfig || {}).num_max_value;
    },
    // 自定义配置项，是否使用控制按钮
    checkedControls() {
      return (this.customConfig || {}).checked
    },
    // 是否显示条形码扫描器
    showBarcodeScanner() {
      // 如果没有配置或者配置为true，则显示条形码功能
      return true;
    }
  },

  watch: {
    // 编辑态时候监听从基线传入的组件值
    "vueProps.data": function(newVal) {
      if (newVal) {
        this.dataValue = newVal;
        try {
          this.number = JSON.parse(newVal);
        } catch (e) {
          console.warn('解析数据失败:', e);
          this.number = null;
        }
      }
    }
  },

  methods: {
    /**
     * 组件动作-取数字值的回调，对应eventActionDefinitions.js中定义的actions的getNumber组件动作
     * @returns {obj} 返回数字
     */
    getNumber() {
      return {
        number: this.number
      }
    },
    /**
     * 组件动作-取扫描结果的回调，对应eventActionDefinitions.js中定义的actions的getChineseNumber组件动作
     * @returns {obj} 返回扫描结果
     */
    getChineseNumber() {
      return {
        chineseNumber: this.number || ''
      }
    },
    handleChange(val) {
      this.number = val;
      // 内容改变时候给基线传入最新的值,传入基线的值要json化
      if (this.vueProps && this.vueProps.onChange) {
        this.vueProps.onChange(JSON.stringify(val));
      }
      // 内容改变时候触发逻辑控制
      this.triggerEvent("onClick", {
        value: this.number
      });
    },

    // 条形码扫描器相关方法
    // 检测设备类型
    detectDeviceType() {
      // 检测是否为移动设备
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      this.isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      console.log(`设备类型检测: ${this.isMobileDevice ? '移动设备' : '桌面设备'}`);
    },

    // 查询可用的摄像头设备
    async enumerateCameras() {
      try {
        // 检查浏览器是否支持摄像头枚举
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
          console.log('浏览器不支持摄像头枚举');
          return false;
        }

        // 获取所有媒体设备
        const devices = await navigator.mediaDevices.enumerateDevices();

        // 过滤出视频输入设备（摄像头）
        this.availableCameras = devices.filter(device => device.kind === 'videoinput');

        console.log(`检测到 ${this.availableCameras.length} 个摄像头设备:`, this.availableCameras);

        // 如果有多个摄像头，默认选择后置摄像头（移动设备）或第一个摄像头（桌面设备）
        if (this.availableCameras.length > 0) {
          // 如果是移动设备且有多个摄像头，默认选择后置摄像头（通常是最后一个）
          if (this.isMobileDevice && this.availableCameras.length > 1) {
            this.selectedCameraId = this.availableCameras[this.availableCameras.length - 1].deviceId;
          } else {
            // 否则选择第一个摄像头
            this.selectedCameraId = this.availableCameras[0].deviceId;
          }
          return true;
        } else {
          console.warn('未检测到摄像头设备');
          return false;
        }
      } catch (error) {
        console.error('枚举摄像头设备时出错:', error);
        return false;
      }
    },

    // 切换摄像头
    async switchCamera() {
      if (this.availableCameras.length <= 1) {
        console.log('只有一个摄像头，无法切换');
        return;
      }

      // 停止当前扫描
      this.stopScanning();

      // 切换前后摄像头模式
      this.useFrontCamera = !this.useFrontCamera;

      // 在移动设备上，根据前后摄像头模式选择摄像头
      if (this.isMobileDevice && this.availableCameras.length > 1) {
        if (this.useFrontCamera) {
          // 前置摄像头通常是第一个
          this.selectedCameraId = this.availableCameras[0].deviceId;
        } else {
          // 后置摄像头通常是最后一个
          this.selectedCameraId = this.availableCameras[this.availableCameras.length - 1].deviceId;
        }
      } else {
        // 在桌面设备上，循环选择下一个摄像头
        const currentIndex = this.availableCameras.findIndex(camera => camera.deviceId === this.selectedCameraId);
        const nextIndex = (currentIndex + 1) % this.availableCameras.length;
        this.selectedCameraId = this.availableCameras[nextIndex].deviceId;
      }

      // 重新初始化扫描器
      this.$nextTick(() => {
        this.initQuagga();
      });
    },

    // 开始扫描
    async startScan() {
      // 检测设备类型
      this.detectDeviceType();

      // 检查环境
      const isHttps = window.location.protocol === 'https:';
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

      // 如果在移动设备上且不是HTTPS且不是localhost，显示警告
      if (this.isMobileDevice && !isHttps && !isLocalhost) {
        console.warn('移动设备必须在HTTPS环境下才能访问摄像头');
        this.cameraError = true;

        // 获取当前域名和路径，生成HTTPS URL
        const currentHost = window.location.host;
        const currentPath = window.location.pathname;
        const httpsUrl = `https://${currentHost}${currentPath}`;

        this.cameraErrorMessage = `移动设备必须在HTTPS环境下才能访问摄像头。<br><br>请尝试使用以下链接访问：<br><a href="${httpsUrl}" style="color: #409EFF;">${httpsUrl}</a><br><br>或联系网站管理员将网站部署到HTTPS环境。`;

        this.showScanner = true;
        return;
      }

      // 显示扫描器界面
      this.showScanner = true;
      this.cameraError = false;
      this.flashOn = false; // 重置闪光灯状态

      // 枚举可用的摄像头设备
      const hasCameras = await this.enumerateCameras();
      if (!hasCameras) {
        this.handleCameraError(new Error('未检测到摄像头设备'));
        return;
      }

      // 检查闪光灯支持
      this.checkFlashSupport();

      // 确保DOM已更新后再初始化扫描器
      this.$nextTick(() => {
        this.initQuagga();
      });
    },

    // 消息提示
    message({value}) {
      alert(value);
    },

    // 处理输入变化
    onInputChange() {
      // 当用户手动输入时，触发数据更新
      if (this.number) {
        // 内容改变时候给基线传入最新的值,传入基线的值要json化
        if (this.vueProps && this.vueProps.onChange) {
          this.vueProps.onChange(JSON.stringify(this.number));
        }
        // 内容改变时候触发逻辑控制
        this.triggerEvent("onClick", {
          value: this.number
        });
        this.$emit('barcode-input', this.number);
      }
    },

    // 清除输入内容
    clearInput() {
      this.number = null;
      this.scanResult = null;
      // 内容清除时候给基线传入最新的值
      if (this.vueProps && this.vueProps.onChange) {
        this.vueProps.onChange(JSON.stringify(null));
      }
      // 内容清除时候触发逻辑控制
      this.triggerEvent("onClick", {
        value: this.number
      });
      this.$emit('barcode-cleared');
    },

    // 初始化Quagga
    initQuagga() {
      if (this.scanning) {
        return;
      }

      this.scanning = true;
      this.lastResults = []; // 重置验证数组
      this.allDetectedCodes = []; // 重置所有检测到的条形码
      this.scanStartTime = Date.now(); // 记录扫描开始时间
      this.formatWarningShown = false; // 重置警告状态

      // 获取配置参数，如果有的话
      const userConfidenceThreshold = this.customConfig.confidenceThreshold || 0.7;

      // 准备摄像头约束条件
      let cameraConstraints = {
        width: { min: 640, ideal: 1280, max: 1920 },
        height: { min: 480, ideal: 720, max: 1080 },
        aspectRatio: { min: 1, max: 2 }
      };

      // 根据设备类型和选择的摄像头设置约束条件
      if (this.selectedCameraId) {
        // 如果有选定的摄像头ID，优先使用该ID
        cameraConstraints.deviceId = { exact: this.selectedCameraId };
        console.log(`使用指定摄像头: ${this.selectedCameraId}`);
      } else if (this.isMobileDevice) {
        // 如果是移动设备但没有指定摄像头ID，使用facingMode
        cameraConstraints.facingMode = this.useFrontCamera ? "user" : "environment";
        console.log(`使用${this.useFrontCamera ? '前置' : '后置'}摄像头`);
      }

      // 初始化Quagga配置
      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: document.querySelector("#interactive"),
          constraints: cameraConstraints,
          area: { // 定义扫描区域，增加中心区域的权重
            top: "0%",
            right: "0%",
            left: "0%",
            bottom: "0%"
          },
        },
        locator: {
          patchSize: "large", // 使用更大的补丁尺寸提高准确性
          halfSample: true,
          debug: {
            showCanvas: false, // 生产环境不显示调试信息
            showPatches: false,
            showFoundPatches: false
          }
        },
        numOfWorkers: navigator.hardwareConcurrency || 4,
        frequency: 10,
        decoder: {
          readers: [
            "code_128_reader",
            "ean_reader",
            "ean_8_reader",
            "code_39_reader",
            "code_39_vin_reader",
            "codabar_reader",
            "upc_reader",
            "upc_e_reader",
            "i2of5_reader"
          ],
          multiple: false, // 只识别一个条形码
          debug: {
            showCanvas: false, // 生产环境不显示调试信息
            showPatches: false,
            showFoundPatches: false,
            showSkeleton: false,
            showLabels: false,
            showPatchLabels: false,
            showRemainingPatchLabels: false,
            boxFromPatches: {
              showTransformed: false,
              showTransformedBox: false,
              showBB: false
            }
          }
        },
        locate: true,
        // 设置条形码识别的阈值
        threshold: userConfidenceThreshold
      }, (err) => {
        if (err) {
          console.error("Quagga初始化失败:", err);
          this.handleCameraError(err);
          return;
        }

        console.log("Quagga初始化成功");
        this.quaggaInitialized = true;

        // 注册条形码检测事件
        Quagga.onDetected(this.handleDetected);
        Quagga.onProcessed(this.handleProcessed);

        // 开始扫描
        Quagga.start();

        // 获取并存储视频流引用，用于闪光灯控制
        try {
          const videoElement = document.querySelector('#interactive video');
          if (videoElement && videoElement.srcObject) {
            this.stream = videoElement.srcObject;
          }
        } catch (e) {
          console.error('获取视频流失败:', e);
        }
      });
    },

    // 处理条形码检测结果
    handleDetected(result) {
      if (!result || !result.codeResult) return;

      const code = result.codeResult.code;
      if (!code) return;

      // 安全地获取可信度
      const confidence = result.codeResult.confidence !== undefined ? result.codeResult.confidence : 0;

      console.log("检测到条形码:", code, "可信度:", confidence);

      // 即使可信度低，也将结果添加到最近结果数组中
      // 这样如果同一个条形码多次被识别，即使可信度低也会被认为有效
      this.lastResults.push(code);

      // 只保留最近的5个结果
      if (this.lastResults.length > 5) {
        this.lastResults.shift();
      }

      // 添加到所有检测到的条形码数组
      this.allDetectedCodes.push(code);

      // 检查扫描时间和不同条形码的数量
      this.checkFormatWarning();

      // 验证结果 - 检查是否有连续多次相同的结果
      // 使用在initQuagga中获取的配置值
      const requiredConsistentResults = this.customConfig.requiredConsistentResults || 2; // 降低为2次，提高识别成功率
      const resultCounts = {};

      // 计算每个结果出现的次数
      this.lastResults.forEach(res => {
        resultCounts[res] = (resultCounts[res] || 0) + 1;
      });

      // 检查是否有任何结果达到了所需的一致性计数
      let validCode = null;
      let maxCount = 0;
      Object.keys(resultCounts).forEach(res => {
        if (resultCounts[res] >= requiredConsistentResults && resultCounts[res] > maxCount) {
          validCode = res;
          maxCount = resultCounts[res];
        }
      });

      // 如果没有有效的结果，检查是否有连续多次出现的相同结果
      if (!validCode && this.lastResults.length >= 3) {
        // 检查最近三次结果是否相同
        if (this.lastResults[this.lastResults.length - 1] === this.lastResults[this.lastResults.length - 2] &&
            this.lastResults[this.lastResults.length - 2] === this.lastResults[this.lastResults.length - 3]) {
          validCode = this.lastResults[this.lastResults.length - 1];
          console.log('连续多次检测到相同条形码，尽管可信度低但认为有效');
        }
      }

      // 特殊处理：检查是否有多次出现的相同条形码，即使不是连续的
      if (!validCode) {
        // 计算每个条形码在所有检测到的条形码中的出现次数
        const allCodeCounts = {};
        this.allDetectedCodes.forEach(c => {
          allCodeCounts[c] = (allCodeCounts[c] || 0) + 1;
        });

        // 找出出现次数最多的条形码
        let mostFrequentCode = null;
        let highestCount = 0;

        Object.keys(allCodeCounts).forEach(c => {
          if (allCodeCounts[c] > highestCount) {
            mostFrequentCode = c;
            highestCount = allCodeCounts[c];
          }
        });

        // 如果出现次数最多的条形码至少出现了两次，则认为有效
        if (mostFrequentCode && highestCount >= 2) {
          validCode = mostFrequentCode;
          console.log(`条形码 ${mostFrequentCode} 是出现次数最多的（${highestCount} 次），认为有效`);
        }
        // 兼容原来的逻辑，如果有条形码出现次数超过4次，也认为有效
        else {
          Object.keys(allCodeCounts).forEach(c => {
            if (allCodeCounts[c] >= 4) {
              validCode = c;
              console.log(`条形码 ${c} 在所有扫描中出现了 ${allCodeCounts[c]} 次，认为有效`);
            }
          });
        }
      }

      // 如果没有有效的结果，继续扫描
      if (!validCode) {
        return;
      }

      // 停止扫描，避免重复检测
      this.stopScanning();

      // 设置扫描结果
      this.scanResult = validCode;

      // 将扫描结果直接设置到 number 变量中
      this.number = validCode;

      // 触发数据更新事件
      if (this.vueProps && this.vueProps.onChange) {
        this.vueProps.onChange(JSON.stringify(validCode));
      }
      this.triggerEvent("onClick", {
        value: this.number
      });

      // 将结果添加到结果数组中，避免重复
      if (!this.scanResults.includes(validCode)) {
        this.scanResults.unshift(validCode); // 将新结果添加到数组开头
      }

      // 触发自定义事件
      this.$emit('barcode-detected', validCode);

      // 如果配置了回调函数，则调用
      if (this.customConfig.onDetected && typeof this.customConfig.onDetected === 'function') {
        this.customConfig.onDetected(validCode);
      }

      // 如果配置了自动关闭，则关闭扫描器
      if (this.customConfig.autoClose !== false) {
        this.closeScanner();
      }
    },

    // 清除扫描结果
    clearResults() {
      this.scanResults = [];
      this.scanResult = null;
    },

    // 处理图像处理结果（用于绘制检测框和图像增强）
    handleProcessed(result) {
      const drawingCtx = Quagga.canvas.ctx.overlay;
      const drawingCanvas = Quagga.canvas.dom.overlay;
      const dynamicTipElement = document.getElementById('dynamicTip');

      if (result) {
        // 清除画布
        drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));

        // 绘制所有检测到的框
        if (result.boxes) {
          result.boxes.filter(function(box) {
            return box !== result.box;
          }).forEach(function(box) {
            Quagga.ImageDebug.drawPath(box, { x: 0, y: 1 }, drawingCtx, { color: "green", lineWidth: 2 });
          });
        }

        // 绘制主要检测框
        if (result.box) {
          Quagga.ImageDebug.drawPath(result.box, { x: 0, y: 1 }, drawingCtx, { color: "#00F", lineWidth: 2 });
        }

        // 绘制条形码线
        if (result.codeResult && result.codeResult.code) {
          // 显示可信度 - 安全地检查confidence是否存在
          if (result.codeResult.confidence !== undefined) {
            const confidence = result.codeResult.confidence.toFixed(2);
            drawingCtx.font = "bold 14px Arial";

            // 检查这个条形码是否多次出现
            const codeCount = this.lastResults.filter(c => c === result.codeResult.code).length;

            // 如果多次出现，即使可信度低也显示为绿色
            const isFrequent = codeCount >= 2;

            // 根据可信度和出现频率决定颜色
            drawingCtx.fillStyle = (confidence > 0.7 || isFrequent) ? "green" : "red";

            // 显示可信度和出现次数
            drawingCtx.fillText(`可信度: ${confidence} | 出现: ${codeCount}次`, 10, 20);

            // 绘制条形码线
            Quagga.ImageDebug.drawPath(result.line, { x: 'x', y: 'y' }, drawingCtx, {
              color: (confidence > 0.7 || isFrequent) ? "green" : "red",
              lineWidth: 3
            });

            // 更新动态提示信息
            if (dynamicTipElement) {
              // 计算每个条形码在所有检测到的条形码中的出现次数
              const allCodeCounts = {};
              this.allDetectedCodes.forEach(c => {
                allCodeCounts[c] = (allCodeCounts[c] || 0) + 1;
              });

              // 找出出现次数最多的条形码
              let mostFrequentCode = null;
              let highestCount = 0;

              Object.keys(allCodeCounts).forEach(c => {
                if (allCodeCounts[c] > highestCount) {
                  mostFrequentCode = c;
                  highestCount = allCodeCounts[c];
                }
              });

              // 显示出现次数最多的条形码信息
              let frequentCodeInfo = '';
              if (mostFrequentCode && highestCount >= 2) {
                frequentCodeInfo = `<div class="frequent-code-info">出现次数最多的条形码: <strong>${mostFrequentCode}</strong> (共${highestCount}次)</div>`;
              }

              if (confidence > 0.7 || isFrequent) {
                dynamicTipElement.innerHTML = `<span class="success-tip">✓ 条形码识别良好，请保持当前位置</span>${frequentCodeInfo}`;
                dynamicTipElement.className = 'dynamic-tip success';
              } else if (confidence > 0.5) {
                dynamicTipElement.innerHTML = `<span class="warning-tip">⚠️ 条形码清晰度一般，请尝试调整距离或角度</span>${frequentCodeInfo}`;
                dynamicTipElement.className = 'dynamic-tip warning';
              } else {
                dynamicTipElement.innerHTML = `<span class="error-tip">✗ 条形码不清晰，请调整光线或距离</span>${frequentCodeInfo}`;
                dynamicTipElement.className = 'dynamic-tip error';
              }
            }
          } else {
            // 如果没有可信度信息，使用默认颜色
            drawingCtx.font = "bold 14px Arial";
            drawingCtx.fillStyle = "orange";
            drawingCtx.fillText(`正在分析...`, 10, 20);

            // 使用默认颜色绘制线
            if (result.line) {
              Quagga.ImageDebug.drawPath(result.line, { x: 'x', y: 'y' }, drawingCtx, {
                color: "orange",
                lineWidth: 3
              });
            }

            // 更新动态提示
            if (dynamicTipElement) {
              dynamicTipElement.innerHTML = '<span class="processing-tip">🔍 正在分析条形码，请稍候...</span>';
              dynamicTipElement.className = 'dynamic-tip processing';
            }
          }
        } else {
          // 没有检测到条形码
          if (dynamicTipElement) {
            // 计算扫描时间（秒）
            const scanDuration = (Date.now() - this.scanStartTime) / 1000;

            if (scanDuration > 3) {
              dynamicTipElement.innerHTML = '<span class="searching-tip">🔍 未检测到条形码，请将条形码对准相机</span>';
              dynamicTipElement.className = 'dynamic-tip searching';
            } else {
              dynamicTipElement.innerHTML = '<span class="searching-tip">🔍 正在搜索条形码...</span>';
              dynamicTipElement.className = 'dynamic-tip searching';
            }
          }
        }

        // 尝试增强图像 - 应用于视频流
        try {
          // 安全地检查Quagga.canvas和dom属性
          if (Quagga.canvas && Quagga.canvas.dom && Quagga.canvas.dom.binary) {
            const videoCanvas = Quagga.canvas.dom.binary;
            if (videoCanvas) {
              const ctx = videoCanvas.getContext('2d');
              if (ctx) {
                // 增强对比度
                ctx.filter = 'contrast(1.4) brightness(1.1)';
              }
            }
          }
        } catch (e) {
          console.log("图像增强失败:", e);
        }
      }
    },

    // 处理相机错误
    handleCameraError(err) {
      this.scanning = false;
      this.cameraError = true;
      console.error("相机访问错误:", err);

      // 检查环境
      const isHttps = window.location.protocol === 'https:';
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isAndroid = /Android/i.test(navigator.userAgent);

      if ((isMobile && !isHttps && !isLocalhost) || (isAndroid && !isHttps && !isLocalhost)) {
        // 获取当前域名和路径，生成HTTPS URL
        const currentHost = window.location.host;
        const currentPath = window.location.pathname;
        const httpsUrl = `https://${currentHost}${currentPath}`;

        this.cameraErrorMessage = `移动设备必须在HTTPS环境下才能访问摄像头。<br><br>请尝试使用以下链接访问：<br><a href="${httpsUrl}" style="color: #409EFF;">${httpsUrl}</a><br><br>或联系网站管理员将网站部署到HTTPS环境。`;
      } else {
        this.cameraErrorMessage = '无法访问摄像头，请确保已授予摄像头权限或在HTTPS环境下访问';
      }
    },

    // 停止扫描
    stopScanning() {
      if (this.quaggaInitialized && Quagga) {
        try {
          Quagga.offDetected(this.handleDetected);
          Quagga.offProcessed(this.handleProcessed);
          Quagga.stop();
          this.scanning = false;
          console.log("扫描已停止");
        } catch (error) {
          console.error("停止扫描时出错:", error);
        }
      }
    },

    // 关闭扫描器
    closeScanner() {
      this.stopScanning();
      this.showScanner = false;
      this.quaggaInitialized = false;
      this.turnOffFlash(); // 关闭闪光灯
    },

    // 检查是否支持闪光灯
    checkFlashSupport() {
      if (navigator.mediaDevices && navigator.mediaDevices.getSupportedConstraints) {
        const supportedConstraints = navigator.mediaDevices.getSupportedConstraints();
        this.hasFlash = supportedConstraints.torch || supportedConstraints.flashMode;
      } else {
        this.hasFlash = false;
      }
    },

    // 切换闪光灯状态
    toggleFlash() {
      if (!this.stream) return;

      try {
        const tracks = this.stream.getVideoTracks();
        if (!tracks || !tracks.length) return;

        const track = tracks[0];
        if (track.getCapabilities && track.getCapabilities().torch) {
          this.flashOn = !this.flashOn;
          track.applyConstraints({
            advanced: [{ torch: this.flashOn }]
          });
        } else {
          console.log('当前设备不支持闪光灯控制');
        }
      } catch (e) {
        console.error('控制闪光灯失败:', e);
      }
    },

    // 关闭闪光灯
    turnOffFlash() {
      if (this.flashOn && this.stream) {
        try {
          const tracks = this.stream.getVideoTracks();
          if (tracks && tracks.length) {
            const track = tracks[0];
            if (track.getCapabilities && track.getCapabilities().torch) {
              track.applyConstraints({
                advanced: [{ torch: false }]
              });
              this.flashOn = false;
            }
          }
        } catch (e) {
          console.error('关闭闪光灯失败:', e);
        }
      }
    },

    // 检查是否需要显示格式警告
    checkFormatWarning() {
      // 如果已经显示过警告，不再显示
      if (this.formatWarningShown) return;

      // 计算扫描时间（秒）
      const scanDuration = (Date.now() - this.scanStartTime) / 1000;

      // 如果扫描时间超过5秒
      if (scanDuration > 5) {
        // 获取不同的条形码数量
        const uniqueCodes = [...new Set(this.allDetectedCodes)];

        // 如果检测到的条形码超过5个且不同的条形码超过3个
        if (this.allDetectedCodes.length > 5 && uniqueCodes.length > 3) {
          // 显示警告
          this.formatWarningShown = true;

          // 在界面上显示警告
          const drawingCtx = Quagga.canvas.ctx.overlay;
          if (drawingCtx) {
            drawingCtx.font = "bold 16px Arial";
            drawingCtx.fillStyle = "red";
            drawingCtx.fillText("条形码格式不正确或不清晰", 10, 40);
          }

          // 更新动态提示
          const dynamicTipElement = document.getElementById('dynamicTip');
          if (dynamicTipElement) {
            dynamicTipElement.innerHTML = `
              <span class="error-tip">⚠️ 条形码识别困难</span>
              <div class="tip-details">
                <p>请尝试以下操作：</p>
                <ul>
                  <li>确保条形码完整且没有损坏</li>
                  <li>调整光线，避免强光反射或光线不足</li>
                  <li>保持手机稳定，不要晃动</li>
                  <li>将条形码水平放置，保持平整</li>
                </ul>
              </div>
            `;
            dynamicTipElement.className = 'dynamic-tip error expanded';
          }
        }
      }
    }
  },

  // 组件销毁时停止扫描
  beforeDestroy() {
    this.stopScanning();
  }
};
</script>

<style lang="less" scoped>
.chinese-num-box {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

// 条形码扫描器样式
.barcode-scanner-container {
  font-family: Arial, sans-serif;
  max-width: 100%;
  margin: 0 auto;
  width: 100%;
}

/* 文本框和扫描按钮的容器 */
.input-scan-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 0;
  width: 100%;
}

/* 文本框包装器 */
.input-wrapper {
  position: relative;
  flex: 1;
  min-width: 0;
}

/* 文本框样式 */
.scan-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 40px; /* 为清除按钮留出空间 */
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  font-family: monospace;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  background-color: #fff;
  box-sizing: border-box;
}

.scan-input:focus {
  outline: none;
  border-color: #409EFF;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}

.scan-input::placeholder {
  color: #999;
  font-family: Arial, sans-serif;
  letter-spacing: normal;
}

/* 清除按钮 */
.clear-input-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-input-button:hover {
  background-color: #f5f5f5;
  color: #666;
}

.clear-input-button:active {
  background-color: #e0e0e0;
}

/* 扫描按钮样式 */
.scan-icon {
  color: #666;
  cursor: pointer;
  padding: 8px;
  background: none;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  flex-shrink: 0;
}

.scan-icon:hover {
  color: #409EFF;
}

.scan-icon:active {
  color: #333;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .input-scan-container {
    gap: 8px;
    padding: 10px 0;
  }

  .scan-input {
    padding: 10px 12px;
    padding-right: 36px;
    font-size: 14px;
  }

  .scan-icon {
    width: 48px;
    height: 48px;
    padding: 10px;
  }

  .scan-icon svg {
    width: 24px;
    height: 24px;
  }
}

.scan-button {
  background-color: #409EFF;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.scanner-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scanner-container {
  background-color: white;
  width: 90%;
  max-width: 600px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.scanner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.scanner-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.scanner-content {
  padding: 15px;
  overflow-y: auto;
}

.camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.viewport {
  width: 100%;
  height: 300px;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.scan-tip {
  margin-top: 10px;
  color: #333;
  text-align: left;
  font-size: 14px;
  background-color: #f8f8f8;
  border-left: 4px solid #409EFF;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scan-tip p {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  color: #409EFF;
}

.scan-tip ul {
  margin: 0;
  padding-left: 20px;
}

.scan-tip li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.dynamic-tip {
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  transition: all 0.3s ease;
}

.dynamic-tip.success {
  background-color: #f0f9eb;
  border-left: 4px solid #67c23a;
  color: #67c23a;
}

.dynamic-tip.warning {
  background-color: #fdf6ec;
  border-left: 4px solid #e6a23c;
  color: #e6a23c;
}

.dynamic-tip.error {
  background-color: #fef0f0;
  border-left: 4px solid #f56c6c;
  color: #f56c6c;
}

.dynamic-tip.error.expanded {
  text-align: left;
  padding: 15px;
}

.tip-details {
  margin-top: 10px;
  font-weight: normal;
  font-size: 14px;
}

.tip-details p {
  margin-top: 0;
  margin-bottom: 8px;
}

.tip-details ul {
  margin: 0;
  padding-left: 20px;
}

.tip-details li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.dynamic-tip.processing, .dynamic-tip.searching {
  background-color: #f4f4f5;
  border-left: 4px solid #909399;
  color: #909399;
}

.success-tip, .warning-tip, .error-tip, .processing-tip, .searching-tip {
  display: inline-block;
  animation: pulse 1.5s infinite;
}

.frequent-code-info {
  margin-top: 8px;
  font-size: 14px;
  font-weight: normal;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 5px 8px;
  border-radius: 3px;
  color: #333;
}

.frequent-code-info strong {
  font-family: monospace;
  letter-spacing: 1px;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.camera-error-container {
  text-align: center;
  padding: 20px;
}

.camera-error-message {
  color: #f56c6c;
  margin-bottom: 15px;
}

.retry-button {
  background-color: #409EFF;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.result-container {
  margin-top: 20px;
  padding: 15px;
  border: 2px solid #409EFF;
  border-radius: 4px;
  background-color: #f0f9ff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-container h3 {
  margin: 0;
  font-size: 18px;
  color: #409EFF;
  font-weight: bold;
}

.clear-button {
  background-color: #f56c6c;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.result-item {
  display: flex;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.result-number {
  background-color: #409EFF;
  color: white;
  padding: 10px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.result-content {
  word-break: break-all;
  font-family: monospace;
  padding: 10px 15px;
  font-size: 16px;
  color: #333;
  letter-spacing: 1px;
  flex: 1;
}

.scan-actions,
.error-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  width: 100%;
}

.action-button {
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  border: none;
  background-color: #f0f0f0;
  color: #333;
}
</style>
