<template>
  <div>
    <el-form ref="form" :model="form" label-width="130px">
      <el-form-item label="插件自定义配置项">
        <el-checkbox v-model="form.checked">是否使用控制按钮</el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        checked: false,
      },
    };
  },
  watch: {
    "form.checked": function () {
      this.onFormLayoutChange();
    },
  },
  props: {
    vueProps: Object,
  },
  methods: {
    onFormLayoutChange() {
      this.vueProps.changeCustomConfig(JSON.stringify(this.form));
    },
  },
  mounted() {
    this.form = this.vueProps.customConfig || {};
  },
};
</script>
